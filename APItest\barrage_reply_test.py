#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dify AI 弹幕回复工作流接口测试脚本
基于streaming模式避免504超时错误
提取Reply参数值并打印到控制台
"""

import requests
import json
import time
import os
import re
from datetime import datetime

def parse_sse_response(response):
    """
    解析SSE（Server-Sent Events）流式响应
    专门提取Reply参数
    """
    print("📡 开始接收流式数据...")

    full_data = {}
    event_count = 0
    reply_content = None

    try:
        for line in response.iter_lines(decode_unicode=True):
            if line:
                line = line.strip()

                # SSE格式：data: {...}
                if line.startswith('data: '):
                    event_count += 1
                    data_content = line[6:]  # 移除 "data: " 前缀

                    # 显示进度
                    if event_count % 5 == 0:
                        print(f"📊 已接收 {event_count} 个事件...")

                    try:
                        # 解析JSON数据
                        event_data = json.loads(data_content)

                        # 合并数据
                        if 'data' in event_data:
                            full_data.update(event_data)

                        # 检查是否是最终结果
                        if event_data.get('event') == 'workflow_finished':
                            print("✅ 工作流完成！")
                            # 尝试提取Reply参数
                            if 'data' in event_data and 'outputs' in event_data['data']:
                                reply_content = event_data['data']['outputs'].get('Reply')
                            break
                        elif event_data.get('event') == 'workflow_started':
                            print("🚀 工作流已启动...")
                        elif event_data.get('event') == 'node_started':
                            node_title = event_data.get('data', {}).get('title', '未知节点')
                            print(f"🔧 节点启动: {node_title}")
                        elif event_data.get('event') == 'node_finished':
                            node_title = event_data.get('data', {}).get('title', '未知节点')
                            print(f"✅ 节点完成: {node_title}")

                    except json.JSONDecodeError:
                        # 可能是非JSON数据，跳过
                        continue

        print(f"📈 总共接收了 {event_count} 个事件")
        
        # 如果没有从workflow_finished事件中获取到Reply，尝试从full_data中获取
        if not reply_content and full_data:
            reply_content = full_data.get('data', {}).get('outputs', {}).get('Reply')
        
        return full_data, reply_content

    except Exception as e:
        print(f"❌ 解析SSE响应时出错: {str(e)}")
        return None, None

def test_barrage_reply_workflow():
    """
    测试Dify AI弹幕回复工作流接口
    """
    # API配置
    url = "https://api.dify.ai/v1/workflows/run"

    # 请求头配置
    headers = {
        "Authorization": "Bearer app-1O9tMgPZHShD70af8SE2mZOB",
        "Content-Type": "application/json",
        "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        "Accept": "*/*",
        "Host": "api.dify.ai",
        "Connection": "keep-alive"
    }
    
    # 请求数据
    data = {
        "inputs": {
            "barrage": "主播，如果我买了没有去可以退吗？",
            "Product_Information_List": " \"productName\": \"海南度假30天29晚舒适经济房+包物业网络\",\n  \"price\": \"380\",\n  \"description\": \"海南度假30天29晚舒适经济房+包物业网络\",\n  \"details\": \"22平度假民宿29晚\\n管家服务冰箱+空调+洗衣机\\n免费WIFI\\n电视机\\n24小时热水\\n包物业费\\n一号岛海洋乐园游玩指南\\n一号岛水上王国游玩指南\",\n  \"purchaseNotes\": \"1.预约有效期\\n2025.02.12至2025.09.30\\n2.可入住日期\\n2025.02.12至2025.09.30\\n3.可用时间\\n最早入住时间14:00，最晚离店时间12:00\\n4.加价规则\\n套餐外增加项目，具体金额需到店支付\\n加价项目\\n押金(离店退)\\n金额\\n1000元/1间\\n5.预约规则\\n需至少提前1天预约\\n6.购买限制\\n每单限购1份\\n7.退款规则\\n下单未预约，可全额退款(过期未约自动退)\\n预约成功后不可取消或修改\\n8.其他规则\\n最早入住时间:14:00。\\n最晚退房时间:12:00。\\n不与店内优惠同享\""
        },
        "response_mode": "streaming",
        "user": "abc-123"
    }
    
    print("=" * 60)
    print("🚀 开始测试 Dify AI 弹幕回复工作流接口")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 请求URL: {url}")
    print(f"👤 用户ID: {data['user']}")
    print(f"💬 弹幕内容: {data['inputs']['barrage']}")
    print("🌊 使用流式模式，实时接收数据...")
    print("⚙️ 无需担心504超时，支持长时间运行")
    print("=" * 60)

    try:
        # 创建会话
        session = requests.Session()
        session.headers.update(headers)

        # 记录开始时间
        start_time = time.time()

        # 发送POST请求，设置3分钟超时
        print("📡 正在发送请求...")
        print("⏳ 等待服务器响应（最多3分钟）...")

        response = session.post(
            url=url,
            json=data,
            timeout=(30, 300),  # 连接超时30秒，读取超时300秒（5分钟）
            stream=True  # 启用流式响应
        )

        print(f"📊 响应状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ 开始接收流式响应...")
            print("=" * 60)

            # 解析SSE流式响应
            response_data, reply_content = parse_sse_response(response)

            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            print(f"✅ 流式响应完成，总耗时: {duration:.2f}秒 ({duration/60:.2f}分钟)")
            print("=" * 60)

            # 打印Reply内容
            if reply_content:
                print("🎯 Reply参数内容:")
                print("=" * 60)
                print(reply_content)
                print("=" * 60)
            else:
                print("⚠️  未找到Reply参数")

            # 可选：打印完整响应数据（调试用）
            if response_data:
                print("📄 完整响应数据:")
                print("=" * 60)
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            else:
                print("⚠️  未能解析流式响应数据")
        else:
            print(f"❌ 流式响应失败，状态码: {response.status_code}")
            # 尝试读取错误信息
            try:
                error_content = response.text
                print("错误内容:")
                print(error_content[:500] + "..." if len(error_content) > 500 else error_content)
            except:
                print("无法读取错误内容")

        print("=" * 60)

        # 根据状态码和数据判断请求是否成功
        if response.status_code == 200 and reply_content:
            print("🎉 弹幕回复接口调用成功！")
            return True, reply_content
        else:
            print(f"❌ 接口调用失败，状态码: {response.status_code}")
            if response.status_code == 504:
                print("💡 提示：服务器超时，请稍后重新运行脚本")
            return False, None

    except requests.exceptions.Timeout:
        print("⏰ 请求超时！接口响应时间超过5分钟")
        print("💡 提示：请重新运行脚本，或检查网络连接")
        return False, None
    except requests.exceptions.ConnectionError:
        print("🌐 连接错误！请检查网络连接")
        return False, None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
        return False, None
    except Exception as e:
        print(f"💥 未知错误: {str(e)}")
        return False, None

def main():
    """
    主函数
    """
    print("🤖 Dify AI 弹幕回复工作流接口测试工具")
    print("=" * 50)
    print("🌊 使用流式模式（streaming）避免504超时")
    print("⚙️ 已优化配置支持SSE流式响应")
    print("🎯 专门提取Reply参数内容")
    print("=" * 50)

    # 开始测试
    success, reply = test_barrage_reply_workflow()

    if success and reply:
        print("🎊 测试成功完成！")
        print(f"📝 Reply内容: {reply}")
    else:
        print("😞 测试失败，如果是超时请重新运行脚本")

if __name__ == "__main__":
    main()