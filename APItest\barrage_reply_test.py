#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dify AI 弹幕回复工作流接口 GUI 测试工具
基于streaming模式避免504超时错误
提供可视化界面进行弹幕输入和回复生成
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import time
import os
import re
import threading
from datetime import datetime

class BarrageReplyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Dify AI 弹幕回复测试工具")
        self.root.geometry("900x700")

        # 商品信息文件路径
        self.product_info_file = "product_info_list.txt"

        # API调用状态
        self.is_calling_api = False

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="🤖 Dify AI 弹幕回复测试工具",
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 弹幕输入区域
        ttk.Label(main_frame, text="💬 弹幕内容:", font=('Arial', 10, 'bold')).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))

        self.barrage_var = tk.StringVar(value="主播，如果我买了没有去可以退吗？")
        barrage_entry = ttk.Entry(main_frame, textvariable=self.barrage_var, width=60)
        barrage_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        # 商品信息状态显示
        ttk.Label(main_frame, text="📦 商品信息状态:", font=('Arial', 10, 'bold')).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5))

        self.product_status_var = tk.StringVar()
        product_status_label = ttk.Label(main_frame, textvariable=self.product_status_var,
                                        foreground="green")
        product_status_label.grid(row=2, column=1, sticky=tk.W, pady=(0, 10))

        # 控制按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))

        # 生成回复按钮
        self.generate_button = ttk.Button(button_frame, text="🚀 生成弹幕回复",
                                         command=self.generate_reply)
        self.generate_button.pack(side=tk.LEFT, padx=(0, 10))

        # 刷新商品信息按钮
        refresh_button = ttk.Button(button_frame, text="🔄 刷新商品信息",
                                   command=self.update_product_status)
        refresh_button.pack(side=tk.LEFT)

        # 输出区域
        ttk.Label(main_frame, text="📋 输出结果:", font=('Arial', 10, 'bold')).grid(
            row=4, column=0, sticky=(tk.W, tk.N), pady=(0, 5))

        # 创建输出文本框
        self.output_text = scrolledtext.ScrolledText(main_frame, height=25, width=80,
                                                    wrap=tk.WORD, font=('Consolas', 9))
        self.output_text.grid(row=4, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var,
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        # 初始化日志
        self.log_message("🎯 弹幕回复测试工具已启动")
        self.log_message("🌊 使用流式模式（streaming）避免504超时")
        self.log_message("⚙️ 已优化配置支持SSE流式响应")

        # 初始化商品信息状态
        self.update_product_status()

    def update_product_status(self):
        """更新商品信息状态"""
        try:
            if os.path.exists(self.product_info_file):
                with open(self.product_info_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        # 计算文件大小
                        file_size = len(content.encode('utf-8'))
                        self.product_status_var.set(f"✅ 已加载 ({file_size} 字节)")
                        self.log_message(f"📦 商品信息文件已加载: {file_size} 字节")
                    else:
                        self.product_status_var.set("⚠️ 文件为空")
                        self.log_message("⚠️ 商品信息文件为空")
            else:
                self.product_status_var.set("❌ 文件不存在")
                self.log_message("❌ 商品信息文件不存在")
        except Exception as e:
            self.product_status_var.set(f"❌ 读取错误: {str(e)}")
            self.log_message(f"❌ 读取商品信息文件出错: {str(e)}")

    def log_message(self, message):
        """在输出区域添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"

        self.output_text.insert(tk.END, log_entry)
        self.output_text.see(tk.END)
        self.root.update_idletasks()

    def generate_reply(self):
        """生成弹幕回复"""
        if self.is_calling_api:
            messagebox.showwarning("警告", "API调用正在进行中，请稍候...")
            return

        barrage_content = self.barrage_var.get().strip()
        if not barrage_content:
            messagebox.showerror("错误", "请输入弹幕内容！")
            return

        # 在新线程中执行API调用，避免界面冻结
        threading.Thread(target=self._call_api, args=(barrage_content,), daemon=True).start()

    def _call_api(self, barrage_content):
        """调用API生成弹幕回复（在后台线程中执行）"""
        try:
            self.is_calling_api = True
            self.status_var.set("正在调用API...")

            # 禁用生成按钮
            self.generate_button.config(state='disabled')

            # 读取商品信息
            product_info = self._load_product_info()
            if not product_info:
                self.log_message("❌ 无法读取商品信息，API调用终止")
                return

            # API配置
            url = "https://api.dify.ai/v1/workflows/run"
            headers = {
                "Authorization": "Bearer app-1O9tMgPZHShD70af8SE2mZOB",
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "application/json"
            }

            # 请求数据
            data = {
                "inputs": {
                    "barrage": barrage_content,
                    "Product_Information_List": product_info
                },
                "response_mode": "streaming",
                "user": "gui-user"
            }

            self.log_message("📡 正在发送请求...")
            self.log_message(f"💬 弹幕内容: {barrage_content}")
            self.log_message(f"📦 商品信息长度: {len(product_info)} 字符")

            # 发送请求
            session = requests.Session()
            session.headers.update(headers)

            start_time = time.time()

            response = session.post(
                url=url,
                json=data,
                timeout=(30, 300),
                stream=True
            )

            self.log_message(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 200:
                self.log_message("✅ 开始接收流式响应...")

                # 解析SSE响应
                response_data, reply_content = parse_sse_response(response, self)

                # 记录结束时间
                end_time = time.time()
                duration = end_time - start_time
                self.log_message(f"✅ 流式响应完成，总耗时: {duration:.2f}秒 ({duration/60:.2f}分钟)")

                if reply_content:
                    self.log_message("🎯 Reply参数内容:")
                    self.log_message("=" * 60)
                    self.log_message(reply_content)
                    self.log_message("=" * 60)

                    # 在主线程中显示成功消息
                    self.root.after(0, lambda: messagebox.showinfo("成功", f"弹幕回复生成完成！\n\n回复内容:\n{reply_content[:200]}{'...' if len(reply_content) > 200 else ''}"))
                else:
                    self.log_message("⚠️ 未找到Reply参数")
                    self.root.after(0, lambda: messagebox.showwarning("警告", "API响应中未找到Reply内容"))
            else:
                error_msg = f"API调用失败，状态码: {response.status_code}"
                self.log_message(f"❌ {error_msg}")
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        except requests.exceptions.Timeout:
            error_msg = "请求超时，请检查网络连接"
            self.log_message(f"❌ {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        except Exception as e:
            error_msg = f"API调用出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        finally:
            # 恢复按钮状态
            self.is_calling_api = False
            self.status_var.set("就绪")
            self.generate_button.config(state='normal')

    def _load_product_info(self):
        """从文件加载商品信息"""
        try:
            if not os.path.exists(self.product_info_file):
                self.log_message(f"❌ 商品信息文件不存在: {self.product_info_file}")
                return None

            with open(self.product_info_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                self.log_message("❌ 商品信息文件为空")
                return None

            self.log_message(f"✅ 成功加载商品信息: {len(content)} 字符")
            return content

        except Exception as e:
            self.log_message(f"❌ 读取商品信息文件出错: {str(e)}")
            return None

def parse_sse_response(response, gui_instance=None):
    """
    解析SSE（Server-Sent Events）流式响应
    专门提取Reply参数
    """
    def log_msg(msg):
        if gui_instance:
            gui_instance.log_message(msg)
        else:
            print(msg)

    log_msg("📡 开始接收流式数据...")

    full_data = {}
    event_count = 0
    reply_content = None

    try:
        for line in response.iter_lines(decode_unicode=True):
            if line:
                line = line.strip()

                # SSE格式：data: {...}
                if line.startswith('data: '):
                    event_count += 1
                    data_content = line[6:]  # 移除 "data: " 前缀

                    # 显示进度
                    if event_count % 5 == 0:
                        log_msg(f"📊 已接收 {event_count} 个事件...")

                    try:
                        # 解析JSON数据
                        event_data = json.loads(data_content)

                        # 合并数据
                        if 'data' in event_data:
                            full_data.update(event_data)

                        # 检查是否是最终结果
                        if event_data.get('event') == 'workflow_finished':
                            log_msg("✅ 工作流完成！")
                            # 尝试提取Reply参数
                            if 'data' in event_data and 'outputs' in event_data['data']:
                                reply_content = event_data['data']['outputs'].get('Reply')
                            break
                        elif event_data.get('event') == 'workflow_started':
                            log_msg("🚀 工作流已启动...")
                        elif event_data.get('event') == 'node_started':
                            node_title = event_data.get('data', {}).get('title', '未知节点')
                            log_msg(f"🔧 节点启动: {node_title}")
                        elif event_data.get('event') == 'node_finished':
                            node_title = event_data.get('data', {}).get('title', '未知节点')
                            log_msg(f"✅ 节点完成: {node_title}")

                    except json.JSONDecodeError:
                        # 可能是非JSON数据，跳过
                        continue

        log_msg(f"📈 总共接收了 {event_count} 个事件")

        # 如果没有从workflow_finished事件中获取到Reply，尝试从full_data中获取
        if not reply_content and full_data:
            reply_content = full_data.get('data', {}).get('outputs', {}).get('Reply')

        return full_data, reply_content

    except Exception as e:
        log_msg(f"❌ 解析SSE响应时出错: {str(e)}")
        return None, None

def test_barrage_reply_workflow_console():
    """
    控制台模式测试Dify AI弹幕回复工作流接口
    """
    # 读取商品信息
    product_info_file = "product_info_list.txt"
    try:
        if not os.path.exists(product_info_file):
            print(f"❌ 商品信息文件不存在: {product_info_file}")
            return False, None

        with open(product_info_file, 'r', encoding='utf-8') as f:
            product_info = f.read().strip()

        if not product_info:
            print("❌ 商品信息文件为空")
            return False, None

        print(f"✅ 成功加载商品信息: {len(product_info)} 字符")

    except Exception as e:
        print(f"❌ 读取商品信息文件出错: {str(e)}")
        return False, None

    # API配置
    url = "https://api.dify.ai/v1/workflows/run"

    # 请求头配置
    headers = {
        "Authorization": "Bearer app-1O9tMgPZHShD70af8SE2mZOB",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "application/json"
    }

    # 请求数据
    data = {
        "inputs": {
            "barrage": "主播，如果我买了没有去可以退吗？",
            "Product_Information_List": product_info
        },
        "response_mode": "streaming",
        "user": "console-user"
    }
    
    print("=" * 60)
    print("🚀 开始测试 Dify AI 弹幕回复工作流接口")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 请求URL: {url}")
    print(f"👤 用户ID: {data['user']}")
    print(f"💬 弹幕内容: {data['inputs']['barrage']}")
    print(f"📦 商品信息长度: {len(product_info)} 字符")
    print("🌊 使用流式模式，实时接收数据...")
    print("⚙️ 无需担心504超时，支持长时间运行")
    print("=" * 60)

    try:
        # 创建会话
        session = requests.Session()
        session.headers.update(headers)

        # 记录开始时间
        start_time = time.time()

        # 发送POST请求，设置5分钟超时
        print("📡 正在发送请求...")
        print("⏳ 等待服务器响应（最多5分钟）...")

        response = session.post(
            url=url,
            json=data,
            timeout=(30, 300),  # 连接超时30秒，读取超时300秒（5分钟）
            stream=True  # 启用流式响应
        )

        print(f"📊 响应状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ 开始接收流式响应...")
            print("=" * 60)

            # 解析SSE流式响应
            response_data, reply_content = parse_sse_response(response)

            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            print(f"✅ 流式响应完成，总耗时: {duration:.2f}秒 ({duration/60:.2f}分钟)")
            print("=" * 60)

            # 打印Reply内容
            if reply_content:
                print("🎯 Reply参数内容:")
                print("=" * 60)
                print(reply_content)
                print("=" * 60)
            else:
                print("⚠️  未找到Reply参数")

            # 可选：打印完整响应数据（调试用）
            if response_data:
                print("📄 完整响应数据:")
                print("=" * 60)
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            else:
                print("⚠️  未能解析流式响应数据")
        else:
            print(f"❌ 流式响应失败，状态码: {response.status_code}")
            # 尝试读取错误信息
            try:
                error_content = response.text
                print("错误内容:")
                print(error_content[:500] + "..." if len(error_content) > 500 else error_content)
            except:
                print("无法读取错误内容")

        print("=" * 60)

        # 根据状态码和数据判断请求是否成功
        if response.status_code == 200 and reply_content:
            print("🎉 弹幕回复接口调用成功！")
            return True, reply_content
        else:
            print(f"❌ 接口调用失败，状态码: {response.status_code}")
            if response.status_code == 504:
                print("💡 提示：服务器超时，请稍后重新运行脚本")
            return False, None

    except requests.exceptions.Timeout:
        print("⏰ 请求超时！接口响应时间超过5分钟")
        print("💡 提示：请重新运行脚本，或检查网络连接")
        return False, None
    except requests.exceptions.ConnectionError:
        print("🌐 连接错误！请检查网络连接")
        return False, None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
        return False, None
    except Exception as e:
        print(f"💥 未知错误: {str(e)}")
        return False, None

def main_console():
    """
    控制台模式主函数
    """
    print("🤖 Dify AI 弹幕回复工作流接口测试工具 (控制台模式)")
    print("=" * 60)
    print("🌊 使用流式模式（streaming）避免504超时")
    print("⚙️ 已优化配置支持SSE流式响应")
    print("🎯 专门提取Reply参数内容")
    print("📦 自动从 product_info_list.txt 读取商品信息")
    print("=" * 60)

    # 开始测试
    success, reply = test_barrage_reply_workflow_console()

    if success and reply:
        print("🎊 测试成功完成！")
        print(f"📝 Reply内容: {reply}")
    else:
        print("😞 测试失败，如果是超时请重新运行脚本")

def main_gui():
    """
    GUI模式主函数
    """
    root = tk.Tk()
    app = BarrageReplyGUI(root)
    root.mainloop()

def main():
    """
    主函数 - 支持命令行参数选择模式
    """
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--console":
        # 控制台模式
        main_console()
    else:
        # 默认GUI模式
        try:
            main_gui()
        except Exception as e:
            print(f"GUI模式启动失败: {str(e)}")
            print("尝试使用控制台模式...")
            main_console()

if __name__ == "__main__":
    main()