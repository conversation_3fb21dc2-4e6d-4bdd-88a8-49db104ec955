#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dify AI 工作流接口 GUI 测试工具
提供可视化界面进行商品信息输入和话术生成
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import requests
import json
import time
import os
import re
import random
import threading
from datetime import datetime

class DifyAPIGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Dify AI 商品话术生成工具")
        self.root.geometry("1000x800")
        
        # 创建必要的文件夹
        self.scripts_folder = "generated_scripts"
        self.product_info_file = "product_info_list.txt"
        os.makedirs(self.scripts_folder, exist_ok=True)
        
        # 随机抽取控制变量
        self.is_random_playing = False
        self.random_thread = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🤖 Dify AI 商品话术生成工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 商品信息输入区域
        info_frame = ttk.LabelFrame(main_frame, text="📦 商品信息输入", padding="10")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)
        
        # 商品名称
        ttk.Label(info_frame, text="商品名称:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.product_name_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.product_name_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # 价格
        ttk.Label(info_frame, text="价格:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.price_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.price_var, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # 商品描述
        ttk.Label(info_frame, text="商品描述:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.description_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.description_var, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # 详细信息
        ttk.Label(info_frame, text="详细信息:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=2)
        self.details_text = scrolledtext.ScrolledText(info_frame, height=4, width=50)
        self.details_text.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # 购买须知
        ttk.Label(info_frame, text="购买须知:").grid(row=4, column=0, sticky=(tk.W, tk.N), pady=2)
        self.purchase_notes_text = scrolledtext.ScrolledText(info_frame, height=6, width=50)
        self.purchase_notes_text.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # 购买限制
        ttk.Label(info_frame, text="购买限制:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.purchase_restrictions_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.purchase_restrictions_var, width=50).grid(row=5, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        # 保存商品信息按钮
        ttk.Button(button_frame, text="💾 保存商品信息", 
                  command=self.save_product_info).pack(side=tk.LEFT, padx=5)
        
        # 生成话术按钮
        ttk.Button(button_frame, text="🚀 生成话术", 
                  command=self.generate_scripts).pack(side=tk.LEFT, padx=5)
        
        # 查看商品列表按钮
        ttk.Button(button_frame, text="📋 查看商品列表", 
                  command=self.view_product_list).pack(side=tk.LEFT, padx=5)
        
        # 随机抽取控制区域
        random_frame = ttk.LabelFrame(main_frame, text="🎲 随机话术播放", padding="10")
        random_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        random_button_frame = ttk.Frame(random_frame)
        random_button_frame.pack(fill=tk.X)
        
        # 开始随机抽取按钮
        self.start_random_btn = ttk.Button(random_button_frame, text="▶️ 开始随机播放", 
                                          command=self.start_random_play)
        self.start_random_btn.pack(side=tk.LEFT, padx=5)
        
        # 停止随机抽取按钮
        self.stop_random_btn = ttk.Button(random_button_frame, text="⏹️ 停止播放", 
                                         command=self.stop_random_play, state=tk.DISABLED)
        self.stop_random_btn.pack(side=tk.LEFT, padx=5)
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="📄 输出信息", padding="10")
        output_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        
        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(4, weight=1)
        
    def log_message(self, message):
        """在输出区域显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.output_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.output_text.see(tk.END)
        self.root.update()
        
    def save_product_info(self):
        """保存商品信息到txt文件"""
        try:
            product_data = {
                "productName": self.product_name_var.get().strip(),
                "price": self.price_var.get().strip(),
                "description": self.description_var.get().strip(),
                "details": self.details_text.get("1.0", tk.END).strip(),
                "purchaseNotes": self.purchase_notes_text.get("1.0", tk.END).strip(),
                "purchaseRestrictions": self.purchase_restrictions_var.get().strip()
            }
            
            # 验证必填字段
            if not product_data["productName"]:
                messagebox.showerror("错误", "请输入商品名称！")
                return
                
            # 格式化商品信息
            product_info = f"""商品名称: {product_data['productName']}
价格: {product_data['price']}
描述: {product_data['description']}
详细信息: {product_data['details']}
购买须知: {product_data['purchaseNotes']}
购买限制: {product_data['purchaseRestrictions']}
保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # 追加到商品信息文件
            with open(self.product_info_file, 'a', encoding='utf-8') as f:
                f.write(product_info)
                f.write("\n" + "="*50 + "\n\n")
                
            self.log_message(f"✅ 商品信息已保存: {product_data['productName']}")
            messagebox.showinfo("成功", "商品信息保存成功！")
            
        except Exception as e:
            error_msg = f"保存商品信息时出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            
    def view_product_list(self):
        """查看已保存的商品列表"""
        try:
            if not os.path.exists(self.product_info_file):
                messagebox.showinfo("提示", "暂无保存的商品信息")
                return
                
            with open(self.product_info_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 创建新窗口显示商品列表
            list_window = tk.Toplevel(self.root)
            list_window.title("商品信息列表")
            list_window.geometry("600x400")
            
            text_widget = scrolledtext.ScrolledText(list_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert("1.0", content)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            error_msg = f"查看商品列表时出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            
    def generate_scripts(self):
        """生成话术"""
        try:
            # 获取输入数据并清理换行符
            product_data = {
                "productName": self.product_name_var.get().strip().replace('\n', '').replace('\r', ''),
                "price": self.price_var.get().strip(),
                "description": self.description_var.get().strip(),
                "details": self.details_text.get("1.0", tk.END).strip(),
                "purchaseNotes": self.purchase_notes_text.get("1.0", tk.END).strip(),
                "purchaseRestrictions": self.purchase_restrictions_var.get().strip()
            }
            
            # 验证必填字段
            if not product_data["productName"]:
                messagebox.showerror("错误", "请输入商品名称！")
                return
            
            self.log_message("🚀 开始生成话术...")
            
            # 在新线程中执行API调用，避免界面冻结
            threading.Thread(target=self._call_api, args=(product_data,), daemon=True).start()
        
        except Exception as e:
            error_msg = f"生成话术时出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            
    def _call_api(self, product_data):
        """调用API生成话术（在后台线程中执行）"""
        try:
            # API配置
            url = "https://api.dify.ai/v1/workflows/run"
            headers = {
                "Authorization": "Bearer app-WyENkvtMeFLDoqae9s5pkZs4",
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "application/json"
            }
            
            # 请求数据
            data = {
                "inputs": product_data,
                "response_mode": "streaming",
                "user": "gui-user"
            }
            
            self.log_message("📡 正在发送请求...")
            
            # 发送请求
            session = requests.Session()
            session.headers.update(headers)
            
            response = session.post(
                url=url,
                json=data,
                timeout=(30, 300),
                stream=True
            )
            
            if response.status_code == 200:
                self.log_message("✅ 开始接收流式响应...")
                
                # 解析SSE响应
                response_data = self._parse_sse_response(response)
                
                if response_data:
                    # 提取并保存脚本
                    scripts = response_data.get('data', {}).get('outputs', {}).get('productIntroductionScripts', [])
                    
                    if scripts:
                        # 保存到文件
                        safe_filename = self._sanitize_filename(product_data['productName'])
                        file_path = os.path.join(self.scripts_folder, f"{safe_filename}.txt")
                        
                        with open(file_path, 'w', encoding='utf-8') as f:
                            content = '\n\n'.join(scripts)
                            f.write(content)
                            
                        self.log_message(f"✅ 话术已保存到: {file_path}")
                        self.log_message(f"📊 共生成 {len(scripts)} 条话术")
                        
                        # 在主线程中显示成功消息
                        self.root.after(0, lambda: messagebox.showinfo("成功", f"话术生成完成！\n文件保存至: {file_path}"))
                    else:
                        self.log_message("⚠️ 未找到话术数据")
                        self.root.after(0, lambda: messagebox.showwarning("警告", "API响应中未找到话术数据"))
                else:
                    self.log_message("❌ 解析响应数据失败")
                    self.root.after(0, lambda: messagebox.showerror("错误", "解析API响应失败"))
            else:
                error_msg = f"API调用失败，状态码: {response.status_code}"
                self.log_message(f"❌ {error_msg}")
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时，请检查网络连接"
            self.log_message(f"❌ {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        except Exception as e:
            error_msg = f"API调用出错: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            
    def _parse_sse_response(self, response):
        """解析SSE流式响应"""
        full_data = {}
        event_count = 0
        
        try:
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    event_count += 1
                    data_content = line[6:]
                    
                    if event_count % 10 == 0:
                        self.log_message(f"📊 已接收 {event_count} 个事件...")
                    
                    try:
                        event_data = json.loads(data_content)
                        
                        if 'data' in event_data:
                            full_data.update(event_data)
                            
                        if event_data.get('event') == 'workflow_finished':
                            self.log_message("✅ 工作流完成！")
                            break
                        elif event_data.get('event') == 'workflow_started':
                            self.log_message("🚀 工作流已启动...")
                        elif event_data.get('event') == 'node_started':
                            node_title = event_data.get('data', {}).get('title', '未知节点')
                            self.log_message(f"🔧 节点启动: {node_title}")
                        elif event_data.get('event') == 'node_finished':
                            node_title = event_data.get('data', {}).get('title', '未知节点')
                            self.log_message(f"✅ 节点完成: {node_title}")
                            
                    except json.JSONDecodeError:
                        continue
                        
            self.log_message(f"📈 总共接收了 {event_count} 个事件")
            return full_data
            
        except Exception as e:
            self.log_message(f"❌ 解析SSE响应时出错: {str(e)}")
            return None
            
    def _sanitize_filename(self, filename):
        """清理文件名，移除换行符和不安全字符"""
        # 首先移除所有控制字符（包括换行符、制表符等）
        filename = ''.join(char for char in filename if ord(char) >= 32)
        # 移除或替换Windows文件名中不允许的字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除前后空格
        filename = filename.strip()
        # 限制文件名长度（Windows文件名限制）
        if len(filename) > 200:
            filename = filename[:200]
        # 如果文件名为空，使用默认名称
        if not filename:
            filename = "product_scripts"
        return filename
        
    def start_random_play(self):
        """开始随机播放话术"""
        if not os.path.exists(self.scripts_folder) or not os.listdir(self.scripts_folder):
            messagebox.showwarning("警告", "话术文件夹为空，请先生成一些话术！")
            return
            
        self.is_random_playing = True
        self.start_random_btn.config(state=tk.DISABLED)
        self.stop_random_btn.config(state=tk.NORMAL)
        
        self.log_message("🎲 开始随机播放话术...")
        
        # 在新线程中执行随机播放
        self.random_thread = threading.Thread(target=self._random_play_loop, daemon=True)
        self.random_thread.start()
        
    def stop_random_play(self):
        """停止随机播放话术"""
        self.is_random_playing = False
        self.start_random_btn.config(state=tk.NORMAL)
        self.stop_random_btn.config(state=tk.DISABLED)
        
        self.log_message("⏹️ 已停止随机播放")
        
    def _random_play_loop(self):
        """随机播放循环 - 修改为按文件顺序从每个文件抽取一条话术组成列表循环播放"""
        try:
            while self.is_random_playing:
                # 获取所有话术文件并排序
                script_files = sorted([f for f in os.listdir(self.scripts_folder) if f.endswith('.txt')])
                
                if not script_files:
                    self.log_message("⚠️ 没有找到话术文件")
                    break
                
                # 创建本轮播放列表
                current_playlist = []
                
                # 从每个文件中随机抽取一条话术
                for file_name in script_files:
                    file_path = os.path.join(self.scripts_folder, file_name)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            scripts = [s.strip() for s in content.split('\n\n') if s.strip()]
                        
                        if scripts:
                            # 随机选择一条话术
                            selected_script = random.choice(scripts)
                            current_playlist.append({
                                'file_name': file_name[:-4],  # 去掉.txt后缀
                                'script': selected_script
                            })
                        
                    except Exception as e:
                        self.log_message(f"❌ 读取文件 {file_name} 时出错: {str(e)}")
                        continue
                
                if not current_playlist:
                    self.log_message("⚠️ 没有可播放的话术内容")
                    break
                
                # 显示本轮播放列表开始
                self.log_message("🎲 开始新一轮随机播放话术...")
                self.log_message("=" * 60)
                
                # 按顺序播放列表中的每条话术
                for item in current_playlist:
                    if not self.is_random_playing:
                        break
                    
                    self.log_message(f"🎯 随机抽取 - {item['file_name']}:")
                    self.log_message(item['script'])
                    self.log_message("=" * 60)
                    
                    # 等待3秒后继续（如果仍在播放状态）
                    for _ in range(30):  # 3秒 = 30 * 0.1秒
                        if not self.is_random_playing:
                            break
                        time.sleep(0.1)
                    
                # 本轮播放完成，显示分隔
                if self.is_random_playing:
                    self.log_message("🔄 本轮播放完成，准备下一轮...")
                    self.log_message("=" * 60)
                    
                    # 轮次间隔稍长一些
                    for _ in range(10):  # 1秒间隔
                        if not self.is_random_playing:
                            break
                        time.sleep(0.1)
                    
        except Exception as e:
            self.log_message(f"❌ 随机播放出错: {str(e)}")
        finally:
            # 确保按钮状态正确
            self.root.after(0, lambda: self.stop_random_play())

def main():
    """主函数"""
    root = tk.Tk()
    app = DifyAPIGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()








